//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'dart:io';


import '/app/models/cart.dart';
import '/app/models/cart_line_item.dart';
import '/app/models/checkout_session.dart';
import '/bootstrap/helpers.dart';
import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart';


Future<Map<String, dynamic>> buildOrderWC({bool markPaid = true}) async {
  CheckoutSession checkoutSession = CheckoutSession.getInstance;
  WooOrder orderWC = WooOrder(
    id: 0, // Temporary ID, will be assigned by WooCommerce
  );

  String paymentMethodName = checkoutSession.paymentType!.name;

  orderWC.paymentMethod = Platform.isAndroid
      ? "$paymentMethodName - Android App"
      : "$paymentMethodName - IOS App";

  orderWC.paymentMethodTitle = paymentMethodName.toLowerCase();

  orderWC.setPaid = markPaid;
  orderWC.status = WooOrderStatus.processing;
  // Currency will be handled by WooCommerce backend based on store settings
  // LYD is configured in AppConfig.defaultConfig() currencyMeta
  // Add customer ID when user is authenticated
  WooCustomer? currentUser = await getCurrentUser();
  if (currentUser?.id != null) {
    orderWC.customerId = currentUser!.id!;
    print('🔐 Order assigned to customer ID: ${currentUser.id}');
  } else {
    print('⚠️ Order created as guest (no authenticated user)');
  }

  List<WooLineItem> lineItems = [];
  List<CartLineItem> cartItems = await Cart.getInstance.getCart();
  for (var cartItem in cartItems) {
    WooLineItem lineItem = WooLineItem(
      id: 0, // Will be assigned by WooCommerce
      name: cartItem.name ?? '',
      productId: cartItem.productId ?? 0,
      quantity: cartItem.quantity,
      subtotal: double.tryParse(cartItem.subtotal ?? '0') ?? 0.0,
      total: double.tryParse(cartItem.total ?? '0') ?? 0.0,
    );

    if (cartItem.variationId != null && cartItem.variationId != 0) {
      lineItem.variationId = cartItem.variationId;
    }

    lineItems.add(lineItem);
  }

  orderWC.lineItems = lineItems;

  // Set billing details using WooCommerce API types
  if (checkoutSession.billingDetails?.billingAddress != null) {
    final billingAddress = checkoutSession.billingDetails!.billingAddress!;

    orderWC.billing = WooBilling(
      firstName: billingAddress.firstName,
      lastName: billingAddress.lastName,
      address1: billingAddress.addressLine,
      city: billingAddress.city,
      postcode: billingAddress.postalCode,
      phone: billingAddress.phoneNumber,
      email: billingAddress.emailAddress,
      country: billingAddress.customerCountry?.countryCode ?? 'LY',
    );

    // Set shipping details (same as billing for now)
    orderWC.shipping = WooShipping(
      firstName: billingAddress.firstName,
      lastName: billingAddress.lastName,
      address1: billingAddress.addressLine,
      city: billingAddress.city,
      postcode: billingAddress.postalCode,
      country: billingAddress.customerCountry?.countryCode ?? 'LY',
    );

    print('📋 Order billing/shipping details set');
  } else {
    print('⚠️ No billing details available for order');
  }

  // Add shipping lines when available - CRITICAL FOR WOOCOMMERCE
  if (checkoutSession.shippingType != null) {
    print('🔍 ===== BUILDING SHIPPING LINE FROM CHECKOUT SESSION =====');
    print('📦 CheckoutSession ShippingType:');
    print('   Method ID: ${checkoutSession.shippingType!.methodId}');
    print('   Cost: ${checkoutSession.shippingType!.cost}');
    print('   Object Type: ${checkoutSession.shippingType!.object.runtimeType}');

    Map<String, dynamic>? shippingLineFee = checkoutSession.shippingType!.toShippingLineFee();
    print('📦 Generated Shipping Line Fee: $shippingLineFee');

    if (shippingLineFee != null) {
      // Create WooShippingLine from the shipping type
      // CRITICAL FIX: Ensure total is properly converted to double for WooShippingLine
      var totalValue = shippingLineFee['total'] ?? checkoutSession.shippingType!.cost;
      double? totalAsDouble;

      // Handle different types that might come from shippingLineFee or checkoutSession
      if (totalValue is String) {
        totalAsDouble = double.tryParse(totalValue);
      } else if (totalValue is num) {
        totalAsDouble = totalValue.toDouble();
      }

      WooShippingLine shippingLine = WooShippingLine(
        methodId: shippingLineFee['method_id'] ?? checkoutSession.shippingType!.methodId,
        methodTitle: shippingLineFee['method_title'] ?? 'Shipping',
        total: totalAsDouble, // CRITICAL: Properly typed as double
      );

      orderWC.shippingLines = [shippingLine];
      print('✅ Successfully created WooShippingLine:');
      print('   Method ID: ${shippingLine.methodId}');
      print('   Method Title: ${shippingLine.methodTitle}');
      print('   Total: ${shippingLine.total}');
      print('   Total Type: ${shippingLine.total.runtimeType}');
      print('🔍 Type conversion details:');
      print('   Original totalValue: $totalValue (${totalValue.runtimeType})');
      print('   Converted totalAsDouble: $totalAsDouble (${totalAsDouble.runtimeType})');
    } else {
      print('❌ CRITICAL: Could not create shipping line from shippingType - toShippingLineFee() returned null');
      print('   This will cause "Shipping is required" error in WooCommerce!');
    }
    print('=====================================================');
  } else {
    print('❌ CRITICAL: No shipping type selected - this will cause "Shipping is required" error');
  }
  // Add coupon lines when coupon is applied
  if (checkoutSession.coupon != null) {
    // For now, coupon is handled by WooCommerce automatically when order is created
    // The coupon code will be applied during checkout process
    print('🎫 Order includes coupon: ${checkoutSession.coupon!.code}');
  }

  if (checkoutSession.customerNote?.isNotEmpty ?? false) {
    orderWC.customerNote = checkoutSession.customerNote;
  }

  // PHASE 5: Data Cleansing and Type-Casting Routine (Final Data Schema Fix)
  print('🧹 ===== APPLYING DATA CLEANSING FOR WOOCOMMERCE API =====');

  // Convert to Map for cleansing, then back to WooOrder
  Map<String, dynamic> orderMap = orderWC.toJson();
  print('🔍 Original order map keys: ${orderMap.keys.toList()}');
  print('🔍 Order map size before cleansing: ${orderMap.length} fields');

  // Task 2.1: Fix billing['company'] and shipping['company'] - WooCommerce expects string, not null
  if (orderMap.containsKey('billing') && orderMap['billing'] is Map) {
    if (orderMap['billing']['company'] == null) {
      orderMap['billing']['company'] = '';
      print('🔧 Fixed billing company: null → empty string');
    }
    // INVESTIGATION ZONE 2 - Task 2.1: Enhanced billing address_2 cleanse
    if (orderMap['billing']['address_2'] == null) {
      orderMap['billing']['address_2'] = '';
      print('🔧 Fixed billing address_2: null → empty string');
    }
    // FINAL INVESTIGATION - Task 2.1: Enhanced billing state cleanse
    if (orderMap['billing']['state'] == null) {
      orderMap['billing']['state'] = '';
      print('🔧 Fixed billing state: null → empty string');
    } else if (orderMap['billing']['state'] is! String) {
      orderMap['billing']['state'] = orderMap['billing']['state'].toString();
      print('🔧 Fixed billing state: converted to string: ${orderMap['billing']['state']}');
    }
  }

  if (orderMap.containsKey('shipping') && orderMap['shipping'] is Map) {
    if (orderMap['shipping']['company'] == null) {
      orderMap['shipping']['company'] = '';
      print('🔧 Fixed shipping company: null → empty string');
    }
    // INVESTIGATION ZONE 2 - Task 2.1: Enhanced shipping address_2 cleanse
    if (orderMap['shipping']['address_2'] == null) {
      orderMap['shipping']['address_2'] = '';
      print('🔧 Fixed shipping address_2: null → empty string');
    }
    // FINAL INVESTIGATION - Task 2.1: Enhanced shipping state cleanse
    if (orderMap['shipping']['state'] == null) {
      orderMap['shipping']['state'] = '';
      print('🔧 Fixed shipping state: null → empty string');
    } else if (orderMap['shipping']['state'] is! String) {
      orderMap['shipping']['state'] = orderMap['shipping']['state'].toString();
      print('🔧 Fixed shipping state: converted to string: ${orderMap['shipping']['state']}');
    }
  }

  // Task 2.2: Fix line_items variation_id - WooCommerce expects integer or removal for simple products
  if (orderMap.containsKey('line_items') && orderMap['line_items'] is List) {
    List lineItems = orderMap['line_items'];
    for (int i = 0; i < lineItems.length; i++) {
      var item = lineItems[i];
      if (item is Map && item.containsKey('variation_id')) {
        var variationId = item['variation_id'];
        // Remove variation_id if it's null, 0.0, or any non-integer value for simple products
        if (variationId == null || variationId == 0.0 || (variationId is double && variationId == 0.0)) {
          item.remove('variation_id');
          print('🔧 Removed variation_id from line item $i (was: $variationId)');
        }
      }
    }
  }

  // Task 2.3: Fix shipping_lines id - WooCommerce expects integer instance_id
  if (orderMap.containsKey('shipping_lines') && orderMap['shipping_lines'] is List) {
    List shippingLines = orderMap['shipping_lines'];
    for (int i = 0; i < shippingLines.length; i++) {
      var line = shippingLines[i];
      if (line is Map) {
        // Get instance_id from CheckoutSession.shippingType.object
        if (checkoutSession.shippingType?.object != null) {
          var shippingObject = checkoutSession.shippingType!.object;

          // Extract instance_id from CustomShippingMethod or use default
          int instanceId = 1; // Default fallback

          if (shippingObject.runtimeType.toString() == 'CustomShippingMethod') {
            // For CustomShippingMethod, use a default instance_id since it's custom
            instanceId = 1;
          } else if (shippingObject.runtimeType.toString() == 'ShippingMethod') {
            // For ShippingMethod, try to get instanceId property
            try {
              instanceId = (shippingObject as dynamic).instanceId ?? 1;
            } catch (e) {
              instanceId = 1;
            }
          }

          line['id'] = instanceId;
          print('🔧 Set shipping line $i id to: $instanceId');
        } else {
          // Fallback if no shipping object
          line['id'] = 1;
          print('🔧 Set shipping line $i id to fallback: 1');
        }
      }
    }
  }

  print('✅ Data cleansing completed - order payload ready for WooCommerce API');
  print('🔍 Final verification:');
  print('   Billing company: ${orderMap['billing']?['company']} (${orderMap['billing']?['company'].runtimeType})');
  print('   Shipping company: ${orderMap['shipping']?['company']} (${orderMap['shipping']?['company'].runtimeType})');
  if (orderMap['line_items'] is List && (orderMap['line_items'] as List).isNotEmpty) {
    var firstItem = (orderMap['line_items'] as List)[0];
    print('   First line item variation_id: ${firstItem['variation_id']} (present: ${firstItem.containsKey('variation_id')})');
  }
  if (orderMap['shipping_lines'] is List && (orderMap['shipping_lines'] as List).isNotEmpty) {
    var firstShipping = (orderMap['shipping_lines'] as List)[0];
    print('   First shipping line id: ${firstShipping['id']} (${firstShipping['id'].runtimeType})');
  }
  print('🧹 ========================================================');

  // PHASE 7: Defensive Payload Hydration (WooOrder Model List Handling Fix)
  print('💧 ===== APPLYING DEFENSIVE PAYLOAD HYDRATION =====');

  // Task 1: Implement Payload Hydration for Missing Lists
  // Ensure meta_data is always present as empty list
  if (orderMap['meta_data'] == null) {
    orderMap['meta_data'] = [];
    print('🔧 Hydrated meta_data: null → []');
  }

  // Ensure coupon_lines is always present as empty list
  if (orderMap['coupon_lines'] == null) {
    orderMap['coupon_lines'] = [];
    print('🔧 Hydrated coupon_lines: null → []');
  }

  // Ensure tax_lines is always present as empty list
  if (orderMap['tax_lines'] == null) {
    orderMap['tax_lines'] = [];
    print('🔧 Hydrated tax_lines: null → []');
  }

  // Ensure fee_lines is always present as empty list
  if (orderMap['fee_lines'] == null) {
    orderMap['fee_lines'] = [];
    print('🔧 Hydrated fee_lines: null → []');
  }

  print('✅ Defensive payload hydration completed - all required lists present');
  print('🔍 Final list verification:');
  print('   meta_data: ${orderMap['meta_data']} (type: ${orderMap['meta_data'].runtimeType})');
  print('   coupon_lines: ${orderMap['coupon_lines']} (type: ${orderMap['coupon_lines'].runtimeType})');
  print('   tax_lines: ${orderMap['tax_lines']} (type: ${orderMap['tax_lines'].runtimeType})');
  print('   fee_lines: ${orderMap['fee_lines']} (type: ${orderMap['fee_lines'].runtimeType})');
  print('🛡️ Order payload is now fully hydrated and ready for WooOrder.fromJson()');
  print('💧 ========================================================');

  // PHASE 8: Final Payload Cleansing - Convert Numeric Price Fields to Strings in line_items
  print('💰 ===== APPLYING FINAL LINE ITEMS PRICE CONVERSION =====');

  // Task 1: Implement Final Cleansing for line_items Price Fields
  if (orderMap.containsKey('line_items') && orderMap['line_items'] is List) {
    List<dynamic> lineItems = orderMap['line_items'] as List<dynamic>;
    print('🔍 Processing ${lineItems.length} line items for price field conversion');

    for (int i = 0; i < lineItems.length; i++) {
      if (lineItems[i] is Map<String, dynamic>) {
        Map<String, dynamic> item = lineItems[i] as Map<String, dynamic>;

        // PHASE 10: Deep Payload Cleansing - Cleanse Line Item MetaData
        if (item.containsKey('meta_data') && item['meta_data'] is List) {
          List<dynamic> metaDataList = item['meta_data'] as List<dynamic>;
          print('🔍 Line item $i: Processing ${metaDataList.length} meta_data entries');

          // Inner loop to iterate through each 'meta' object in this line item's 'meta_data' list
          for (int j = 0; j < metaDataList.length; j++) {
            if (metaDataList[j] is Map<String, dynamic>) { // Ensure 'meta' is a Map
              Map<String, dynamic> meta = metaDataList[j] as Map<String, dynamic>;

              // Cleanse the 'value' field: If 'meta['value']' is null, set it to an empty string
              if (meta['value'] == null) {
                meta['value'] = '';
                print('🔧 Line item $i, meta_data[$j]: value was null, set to empty string');
              }
            }
          }
          print('✅ Line item $i: All meta_data value fields cleansed');
        }

        // INVESTIGATION ZONE 2 - Task 2.2: Enhanced line_items tax_class cleanse
        if (item.containsKey('tax_class')) {
          if (item['tax_class'] == null) {
            item['tax_class'] = ''; // Default null to empty string
            print('🔧 Line item $i: tax_class was null, set to empty string');
          } else if (item['tax_class'] is! String) {
            item['tax_class'] = item['tax_class'].toString(); // Ensure it's a string
            print('🔧 Line item $i: tax_class converted to string: ${item['tax_class']}');
          }
        } else {
          item['tax_class'] = ''; // If key doesn't exist, add it as empty string
          print('🔧 Line item $i: tax_class key missing, added as empty string');
        }

        // FINAL INVESTIGATION - Task 2.2: Enhanced line_items subtotal_tax cleanse
        if (item.containsKey('subtotal_tax')) {
          if (item['subtotal_tax'] == null) {
            item['subtotal_tax'] = '';
            print('🔧 Line item $i: subtotal_tax was null, set to empty string');
          } else if (item['subtotal_tax'] is! String) {
            item['subtotal_tax'] = item['subtotal_tax'].toString();
            print('🔧 Line item $i: subtotal_tax converted to string: ${item['subtotal_tax']}');
          }
        } else {
          item['subtotal_tax'] = ''; // Ensure key exists
          print('🔧 Line item $i: subtotal_tax key missing, added as empty string');
        }

        // FINAL INVESTIGATION - Task 2.2: Enhanced line_items total_tax cleanse
        if (item.containsKey('total_tax')) {
          if (item['total_tax'] == null) {
            item['total_tax'] = '';
            print('🔧 Line item $i: total_tax was null, set to empty string');
          } else if (item['total_tax'] is! String) {
            item['total_tax'] = item['total_tax'].toString();
            print('🔧 Line item $i: total_tax converted to string: ${item['total_tax']}');
          }
        } else {
          item['total_tax'] = ''; // Ensure key exists
          print('🔧 Line item $i: total_tax key missing, added as empty string');
        }

        // PHASE 9: Handle null price - default to total value if price is null
        if (item['price'] == null) {
          // If 'price' is null, default it to the 'total' value
          item['price'] = item['total'];
          print('🔧 Line item $i: price was null, defaulted to total: ${item['total']}');
        }

        // Convert price from number to string
        if (item.containsKey('price') && item['price'] is num) {
          String originalPrice = item['price'].toString();
          item['price'] = originalPrice;
          print('🔧 Line item $i: price converted to string: $originalPrice');
        }

        // Convert total from number to string
        if (item.containsKey('total') && item['total'] is num) {
          String originalTotal = item['total'].toString();
          item['total'] = originalTotal;
          print('🔧 Line item $i: total converted to string: $originalTotal');
        }

        // Convert subtotal from number to string
        if (item.containsKey('subtotal') && item['subtotal'] is num) {
          String originalSubtotal = item['subtotal'].toString();
          item['subtotal'] = originalSubtotal;
          print('🔧 Line item $i: subtotal converted to string: $originalSubtotal');
        }
      }
    }

    print('✅ All line items price fields converted to strings');

    // Final verification of first line item (if exists)
    if (lineItems.isNotEmpty && lineItems[0] is Map<String, dynamic>) {
      Map<String, dynamic> firstItem = lineItems[0] as Map<String, dynamic>;
      print('🔍 Final verification - First line item:');
      print('   price: ${firstItem['price']} (type: ${firstItem['price'].runtimeType}, null: ${firstItem['price'] == null})');
      print('   total: ${firstItem['total']} (type: ${firstItem['total'].runtimeType})');
      print('   subtotal: ${firstItem['subtotal']} (type: ${firstItem['subtotal'].runtimeType})');

      // Critical verification: ensure price is never null
      if (firstItem['price'] == null) {
        print('❌ CRITICAL ERROR: price field is still null after hydration!');
      } else {
        print('✅ VERIFIED: price field is properly hydrated and not null');
      }

      // PHASE 10 verification: ensure meta_data value fields are never null
      if (firstItem.containsKey('meta_data') && firstItem['meta_data'] is List) {
        List<dynamic> metaDataList = firstItem['meta_data'] as List<dynamic>;
        print('🔍 Meta_data verification: ${metaDataList.length} entries found');

        bool allMetaDataValid = true;
        for (int j = 0; j < metaDataList.length; j++) {
          if (metaDataList[j] is Map<String, dynamic>) {
            Map<String, dynamic> meta = metaDataList[j] as Map<String, dynamic>;
            if (meta['value'] == null) {
              print('❌ CRITICAL ERROR: meta_data[$j] value field is still null!');
              allMetaDataValid = false;
            }
          }
        }

        if (allMetaDataValid) {
          print('✅ VERIFIED: All meta_data value fields are properly cleansed (non-null)');
        }
      } else {
        print('ℹ️ No meta_data found in first line item');
      }
    }
  } else {
    print('⚠️ No line_items found or line_items is not a List');
  }

  print('🛡️ Line items are now fully hydrated and ready for WooLineItem.fromJson() constructor');
  print('🎯 PHASE 9 COMPLETE: All price fields guaranteed non-null and converted to strings');
  print('🔬 PHASE 10 COMPLETE: All meta_data value fields guaranteed non-null (empty strings)');
  print('🏆 ULTIMATE VICTORY: Deep payload cleansing completed at all levels');
  print('💰 ========================================================');

  // INVESTIGATION ZONE 2 - Task 2.3: Enhanced shipping_lines total cleanse
  print('🚢 ===== APPLYING SHIPPING LINES TOTAL CONVERSION =====');
  if (orderMap.containsKey('shipping_lines') && orderMap['shipping_lines'] is List) {
    List<dynamic> shippingLines = orderMap['shipping_lines'] as List<dynamic>;
    print('🔍 Processing ${shippingLines.length} shipping lines for total field conversion');

    for (int i = 0; i < shippingLines.length; i++) {
      if (shippingLines[i] is Map<String, dynamic>) {
        Map<String, dynamic> shipLine = shippingLines[i] as Map<String, dynamic>;
        if (shipLine.containsKey('total') && shipLine['total'] is num) {
          String originalTotal = shipLine['total'].toString();
          shipLine['total'] = originalTotal;
          print('🔧 Shipping line $i: total converted to string: $originalTotal');
        } else if (!shipLine.containsKey('total') || shipLine['total'] == null) {
          shipLine['total'] = '0.00'; // Default if missing or null, to be safe
          print('🔧 Shipping line $i: total was null/missing, set to "0.00"');
        }

        // FINAL INVESTIGATION - Task 2.3: Enhanced shipping_lines total_tax cleanse
        if (shipLine.containsKey('total_tax')) {
          if (shipLine['total_tax'] == null) {
            shipLine['total_tax'] = '';
            print('🔧 Shipping line $i: total_tax was null, set to empty string');
          } else if (shipLine['total_tax'] is! String) {
            shipLine['total_tax'] = shipLine['total_tax'].toString();
            print('🔧 Shipping line $i: total_tax converted to string: ${shipLine['total_tax']}');
          }
        } else {
          shipLine['total_tax'] = ''; // Ensure key exists
          print('🔧 Shipping line $i: total_tax key missing, added as empty string');
        }
      }
    }
    print('✅ All shipping lines total fields converted to strings');
  } else {
    print('ℹ️ No shipping_lines found or shipping_lines is not a List');
  }
  print('🚢 ========================================================');

  // PHASE 11: The Ultimate Bypass - Return Raw Map Instead of WooOrder
  print('🚀 ===== PHASE 11: ULTIMATE BYPASS ACTIVATED =====');
  print('🔥 Abandoning flawed WooOrder/WooLineItem constructors');
  print('🎯 Returning perfectly cleansed raw Map<String, dynamic>');

  // Ensure status remains as string in the raw map (no enum conversion needed)
  if (!orderMap.containsKey('status') || orderMap['status'] == null) {
    orderMap['status'] = 'processing'; // Default status as string
    print('🔧 Set default status as string: "processing"');
  }

  // FINAL INVESTIGATION - Task 2.4: Add Final Verification Logging
  print('🔍 ===== FINAL CLEANSING CHECK (buildOrderWC return - Critical Fields) =====');
  print('  billing.state: ${orderMap['billing']?['state']} (${orderMap['billing']?['state'].runtimeType})');
  print('  shipping.state: ${orderMap['shipping']?['state']} (${orderMap['shipping']?['state'].runtimeType})');

  if (orderMap.containsKey('line_items') && (orderMap['line_items'] as List).isNotEmpty) {
    var firstLineItem = (orderMap['line_items'] as List)[0] as Map<String, dynamic>;
    print('  line_items[0].subtotal_tax: ${firstLineItem['subtotal_tax']} (${firstLineItem['subtotal_tax'].runtimeType})');
    print('  line_items[0].total_tax: ${firstLineItem['total_tax']} (${firstLineItem['total_tax'].runtimeType})');
  }

  if (orderMap.containsKey('shipping_lines') && (orderMap['shipping_lines'] as List).isNotEmpty) {
    var firstShippingLine = (orderMap['shipping_lines'] as List)[0] as Map<String, dynamic>;
    print('  shipping_lines[0].total_tax: ${firstShippingLine['total_tax']} (${firstShippingLine['total_tax'].runtimeType})');
  }
  print('🔍 =================================================================');

  print('✅ Raw order map is ready for direct API transmission');
  print('🔍 Final map verification:');
  print('   Keys: ${orderMap.keys.toList()}');
  print('   Status: ${orderMap['status']} (${orderMap['status'].runtimeType})');
  print('   Line items count: ${(orderMap['line_items'] as List?)?.length ?? 0}');
  print('   Shipping lines count: ${(orderMap['shipping_lines'] as List?)?.length ?? 0}');
  print('🚀 ===============================================');

  return orderMap;
}
