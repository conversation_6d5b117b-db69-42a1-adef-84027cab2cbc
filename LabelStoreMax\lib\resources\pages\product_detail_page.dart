//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import '/resources/widgets/wishlist_icon_widget.dart';
import '/resources/widgets/store_logo_widget.dart';
import '/app/controllers/product_detail_controller.dart';
import '/app/models/cart_line_item.dart';

import '/bootstrap/helpers.dart';
import '/resources/widgets/buttons.dart';
import '/resources/widgets/cart_icon_widget.dart';
import '/resources/widgets/product_detail_body_widget.dart';
import '/resources/widgets/product_detail_footer_actions_widget.dart';
import '/resources/widgets/velvete_ui.dart';
import 'package:nylo_framework/nylo_framework.dart';

import '/app/models/woocommerce_wrappers/my_product_variation.dart';
import '/app/models/woocommerce_wrappers/my_product.dart';
import '/app/services/woocommerce_service.dart';

class ProductDetailPage extends NyStatefulWidget<ProductDetailController> {
  static RouteView path = ("/product-detail", (_) => ProductDetailPage());

  ProductDetailPage({super.key}) : super(child: () => _ProductDetailState());
}

class _ProductDetailState extends NyPage<ProductDetailPage> {
  MyProduct? _product;

  List<MyProductVariation> _productVariations = [];
  final Map<int, dynamic> _tmpAttributeObj = {};
  // Remove WooSignalApp reference for now

  @override
  get init => () async {
        final data = widget.controller.data();
        WooCommerceService wooCommerceService = WooCommerceService();
        if (data is Map && data.containsKey("productId")) {
          _product = await wooCommerceService.getProduct(data["productId"]);
        } else {
          _product = data;
        }
        widget.controller.product = _product;
        if (_product?.type == "variable") {
          await _fetchProductVariations();
        }
      };

  @override
  LoadingStyle loadingStyle = LoadingStyle.skeletonizer();

  String _getAttributeName(int index) {
    if (_product?.attributes == null || index >= _product!.attributes.length) {
      return "";
    }

    final attribute = _product!.attributes[index];
    // Handle both MyProductAttribute objects and raw Map data
    if (attribute is Map<String, dynamic>) {
      return attribute['name']?.toString() ?? "";
    } else {
      // This should be a MyProductAttribute object with a name property
      try {
        return attribute.name?.toString() ?? "";
      } catch (e) {
        print('⚠️ Error accessing attribute name: $e');
        // Fallback: try to convert to string and extract name
        String attrStr = attribute.toString();
        return attrStr.isNotEmpty ? attrStr : "Unknown Attribute";
      }
    }
  }

  List<String> _getAttributeOptions(int index) {
    if (_product?.attributes == null || index >= _product!.attributes.length) {
      return [];
    }

    final attribute = _product!.attributes[index];
    if (attribute is Map<String, dynamic>) {
      final options = attribute['options'];
      if (options is List) {
        return options.map((option) => option.toString()).toList();
      }
    } else if (attribute != null) {
      // Try to access options property if it exists
      try {
        final options = attribute.options;
        if (options is List) {
          return options.map((option) => option.toString()).toList();
        }
      } catch (e) {
        // Fallback
      }
    }
    return [];
  }

  _fetchProductVariations() async {
    List<MyProductVariation> tmpVariations = [];
    int currentPage = 1;
    WooCommerceService wooCommerceService = WooCommerceService();

    bool isFetching = true;
    if (_product?.id == null) {
      return;
    }
    while (isFetching) {
      List<MyProductVariation> tmp = await wooCommerceService.getProductVariations(
        _product!.id,
        page: currentPage,
        perPage: 100,
      );
      if (tmp.isNotEmpty) {
        tmpVariations.addAll(tmp);
      }

      if (tmp.length >= 100) {
        currentPage += 1;
      } else {
        isFetching = false;
      }
    }
    _productVariations = tmpVariations;
  }

  _modalBottomSheetOptionsForAttribute(int attributeIndex) {
    wsModalBottom(
      context,
      (BuildContext context) => Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        padding: EdgeInsets.all(20),
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.7,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header with close button
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "${trans("Select")} ${_getAttributeName(attributeIndex)}",
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: Icon(Icons.close, color: Colors.grey[600]),
                ),
              ],
            ),
            Divider(color: Colors.grey[300]),
            SizedBox(height: 8),
            Expanded(
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: _getAttributeOptions(attributeIndex).length,
                itemBuilder: (BuildContext context, int index) {
                  final options = _getAttributeOptions(attributeIndex);
                  final optionValue = options[index];
                  final isSelected = (_tmpAttributeObj.isNotEmpty &&
                      _tmpAttributeObj.containsKey(attributeIndex) &&
                      _tmpAttributeObj[attributeIndex]["value"] == optionValue);

                  return Container(
                    margin: EdgeInsets.symmetric(vertical: 4),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? Theme.of(context).primaryColor
                          : Colors.grey[50],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelected
                            ? Theme.of(context).primaryColor
                            : Colors.grey[300]!,
                        width: isSelected ? 2 : 1,
                      ),
                      boxShadow: isSelected ? [
                        BoxShadow(
                          color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
                          blurRadius: 8,
                          offset: Offset(0, 2),
                        ),
                      ] : null,
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(12),
                        onTap: () {
                          _tmpAttributeObj[attributeIndex] = {
                            "name": _getAttributeName(attributeIndex),
                            "value": optionValue
                          };
                          Navigator.pop(context);
                          _modalBottomSheetAttributes();
                        },
                        child: Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                          child: Row(
                            children: [
                              Expanded(
                                child: Text(
                                  optionValue,
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                                    color: isSelected ? Colors.white : Colors.black87,
                                  ),
                                ),
                              ),
                              if (isSelected)
                                Icon(
                                  Icons.check_circle,
                                  color: Colors.white,
                                  size: 24,
                                ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  _modalBottomSheetAttributes() {
    MyProductVariation? productVariation = widget.controller
        .findProductVariation(
            tmpAttributeObj: _tmpAttributeObj,
            productVariations: _productVariations);
    wsModalBottom(
      context,
      (BuildContext context) => Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        padding: EdgeInsets.all(20),
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  trans("Product Options"),
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: Icon(Icons.close, color: Colors.grey[600]),
                ),
              ],
            ),
            Divider(color: Colors.grey[300]),
            SizedBox(height: 8),
            Expanded(
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: _product?.attributes.length ?? 0,
                itemBuilder: (BuildContext context, int index) {
                  final isSelected = (_tmpAttributeObj.isNotEmpty &&
                      _tmpAttributeObj.containsKey(index));
                  final attributeName = _getAttributeName(index);

                  return Container(
                    margin: EdgeInsets.symmetric(vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.grey[50],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelected
                            ? Theme.of(context).primaryColor
                            : Colors.grey[300]!,
                        width: isSelected ? 2 : 1,
                      ),
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(12),
                        onTap: () => _modalBottomSheetOptionsForAttribute(index),
                        child: Padding(
                          padding: EdgeInsets.all(16),
                          child: Row(
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      attributeName,
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w600,
                                        color: Colors.black87,
                                      ),
                                    ),
                                    SizedBox(height: 4),
                                    Text(
                                      isSelected
                                          ? _tmpAttributeObj[index]["value"]
                                          : "${trans("Tap to select")} $attributeName",
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: isSelected
                                            ? Theme.of(context).primaryColor
                                            : Colors.grey[600],
                                        fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Icon(
                                isSelected ? Icons.check_circle : Icons.arrow_forward_ios,
                                color: isSelected
                                    ? Theme.of(context).primaryColor
                                    : Colors.grey[400],
                                size: isSelected ? 24 : 16,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
            Container(
              decoration: BoxDecoration(
                  border: Border(top: BorderSide(color: Colors.black12, width: 1))),
              padding: EdgeInsets.only(top: 10),
              margin: EdgeInsets.only(bottom: 10),
              child: Column(
          children: <Widget>[
            Text(
              (productVariation != null
                  ? "${trans("Price")}: ${formatStringCurrency(total: productVariation.getSafePrice())}"
                  : (((_product?.attributes.length ==
                              _tmpAttributeObj.values.length) &&
                          productVariation == null)
                      ? trans("This variation is unavailable")
                      : trans("Choose your options"))),
              style: Theme.of(context).textTheme.titleMedium,
            ),
            Text(
              (productVariation != null
                  ? !productVariation.isInStock()
                      ? trans("Out of stock")
                      : ""
                  : ""),
              style: Theme.of(context).textTheme.titleMedium,
            ),
            PrimaryButton(
                title: trans("Add to cart"),
                action: () async {
                  // FINAL INVESTIGATION - Task 1.1: Validate product ID before adding to cart
                  if (_product?.id == null || _product?.id == 0) {
                    print('❌ CRITICAL ERROR: Cannot add product with invalid ID: ${_product?.id}');
                    showToast(
                      title: trans("Error"),
                      description: trans("Invalid product. Please try again."),
                      style: ToastNotificationStyleType.danger,
                    );
                    return;
                  }

                  if (_product?.attributes.length !=
                      _tmpAttributeObj.values.length) {
                    showToast(
                        title: trans("Oops"),
                        description: trans("Please select valid options first"),
                        style: ToastNotificationStyleType.warning);
                    return;
                  }

                  if (productVariation == null) {
                    showToast(
                        title: trans("Oops"),
                        description: trans("Product variation does not exist"),
                        style: ToastNotificationStyleType.warning);
                    return;
                  }

                  if (!productVariation.isInStock()) {
                    showToast(
                        title: trans("Sorry"),
                        description: trans("This item is not in stock"),
                        style: ToastNotificationStyleType.warning);
                    return;
                  }

                  List<String> options = [];
                  _tmpAttributeObj.forEach((k, v) {
                    options.add("${v["name"]}: ${v["value"]}");
                  });

                  print('🛒 Creating CartLineItem for variable product: ${_product!.name} (ID: ${_product!.id})');

                  CartLineItem cartLineItem = CartLineItem.fromProductVariation(
                    quantityAmount: widget.controller.quantity,
                    options: options,
                    product: _product!,
                    productVariation: productVariation,
                  );

                  print('✅ CartLineItem created with productId: ${cartLineItem.productId}');

                  await widget.controller.itemAddToCart(
                    cartLineItem: cartLineItem,
                  );
                  pop();
                }),
              ],
            ),
          ),
          ],
        ),
      ),
    );
  }

  @override
  Widget view(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        actions: [
          WishlistIcon(_product),
          CartIconWidget(),
        ],
        title: StoreLogo(
            height: 55,
            showBgWhite: (Theme.of(context).brightness == Brightness.dark)),
        centerTitle: true,
      ),
      body: SafeArea(
          child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          Expanded(
            child: ProductDetailBodyWidget(
              product: _product,
            ),
          ),
          // </Product body>
          ProductDetailFooterActionsWidget(
            onAddToCart: _addItemToCart,
            onViewExternalProduct: widget.controller.viewExternalProduct,
            onAddQuantity: () => widget.controller.addQuantityTapped(),
            onRemoveQuantity: () => widget.controller.removeQuantityTapped(),
            product: _product,
            quantity: widget.controller.quantity,
          )
        ],
      )),
    );
  }

  _addItemToCart() async {
    // FINAL INVESTIGATION - Task 1.1: Validate product ID before adding to cart
    if (_product?.id == null || _product?.id == 0) {
      print('❌ CRITICAL ERROR: Cannot add product with invalid ID: ${_product?.id}');
      showToast(
        title: trans("Error"),
        description: trans("Invalid product. Please try again."),
        style: ToastNotificationStyleType.danger,
      );
      return;
    }

    if (_product?.type != "simple") {
      _modalBottomSheetAttributes();
      return;
    }
    if (_product?.stockStatus != "instock") {
      showToast(
          title: trans("Sorry"),
          description: trans("This item is out of stock"),
          style: ToastNotificationStyleType.warning,
          icon: Icons.local_shipping);
      return;
    }

    print('🛒 Adding to cart: ${_product!.name} (ID: ${_product!.id})');

    await widget.controller.itemAddToCart(
      cartLineItem: CartLineItem.fromProduct(
          quantityAmount: widget.controller.quantity, product: _product!),
    );
  }
}
