//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import '/bootstrap/helpers.dart';
import 'package:nylo_framework/nylo_framework.dart';

import '/app/models/woocommerce_wrappers/my_product.dart';
import '/app/models/woocommerce_wrappers/my_product_category.dart';
import '/app/models/woocommerce_wrappers/my_product_variation.dart';

class CartLineItem {
  String? name;
  int? productId;
  int? variationId;
  int quantity = 0;
  bool? isManagedStock;
  int? stockQuantity;
  String? shippingClassId;
  String? taxStatus;
  String? taxClass;
  bool? shippingIsTaxable;
  String? subtotal;
  String? total;
  String? imageSrc;
  String? variationOptions;
  List<MyProductCategory>? categories;
  bool? onSale;
  String? salePrice;
  String? regularPrice;
  String? stockStatus;
  // FINAL INVESTIGATION - Task 1.2: Add missing fields for WooCommerce API compliance
  String? sku; // Product SKU field
  String? subtotalTax; // Subtotal tax field
  List<Map<String?, dynamic>> metaData = [];
  List<Map<String?, dynamic>> appMetaData = [];

  CartLineItem(
      {this.name,
      this.productId,
      this.variationId,
      this.isManagedStock,
      this.stockQuantity,
      this.quantity = 1,
      this.stockStatus,
      this.shippingClassId,
      this.taxStatus,
      this.taxClass,
      this.categories,
      this.shippingIsTaxable,
      this.variationOptions,
      this.imageSrc,
      this.subtotal,
      this.onSale,
      this.salePrice,
      this.regularPrice,
      this.total,
      this.sku,
      this.subtotalTax,
      this.metaData = const []});

  String getCartTotal() {
    return (quantity * parseWcPrice(subtotal)).toStringAsFixed(2);
  }

  String getPrice({bool formatToCurrency = false}) {
    if (formatToCurrency) {
      return formatStringCurrency(
          total: (parseWcPrice(subtotal)).toStringAsFixed(2));
    }
    return (parseWcPrice(subtotal)).toStringAsFixed(2);
  }

  CartLineItem.fromProduct(
      {int? quantityAmount, required MyProduct product}) {
    name = product.name;
    productId = product.id;
    quantity = quantityAmount ?? 1;
    taxStatus = product.taxStatus;
    shippingClassId = product.shippingClass?.toString();
    subtotal = product.getSafePrice();
    taxClass = product.taxClass;
    categories = product.categories;
    isManagedStock = product.manageStock;
    stockQuantity = product.stockQuantity;
    stockStatus = product.stockStatus; // Fix: Set stock status from product
    salePrice = product.getSafeSalePrice();
    onSale = product.isOnSale();
    regularPrice = product.getSafeRegularPrice();
    shippingIsTaxable = product.shippingTaxable;
    List<Map<String?, String?>> data =
        product.metaData.map((e) => {e.key: e.value}).toList();
    metaData.addAll(data);
    imageSrc = (!product.hasImages())
        ? getEnv("PRODUCT_PLACEHOLDER_IMAGE")
        : product.getFirstImage()?.getSafeImageSrc() ?? getEnv("PRODUCT_PLACEHOLDER_IMAGE");
    total = product.getSafePrice();
    // FINAL INVESTIGATION - Task 1.2: Set SKU from product
    sku = product.sku; // Can be null, will be handled in .toJson()
    subtotalTax = null; // Will be set to empty string in .toJson()
    appMetaData = product.metaData.map((e) => {e.key: e.value}).toList();
  }

  CartLineItem.fromProductVariation(
      {int? quantityAmount,
      required List<String> options,
      required MyProduct product,
      required MyProductVariation productVariation}) {
    String? imageSrc = getEnv("PRODUCT_PLACEHOLDER_IMAGE");
    if (product.images.isNotEmpty) {
      imageSrc = product.images.first.src;
    }
    if (productVariation.image != null) {
      imageSrc = productVariation.image!.getSafeImageSrc();
    }
    name = product.name;
    productId = product.id;
    variationId = productVariation.id;
    quantity = quantityAmount ?? 1;
    taxStatus = productVariation.taxStatus;
    shippingClassId = productVariation.shippingClass?.toString();
    subtotal = productVariation.getSafePrice();
    stockQuantity = productVariation.stockQuantity;
    stockStatus = productVariation.stockStatus; // Fix: Set stock status from variation
    isManagedStock = productVariation.manageStock;
    categories = product.categories;
    taxClass = productVariation.taxClass;
    onSale = productVariation.onSale;
    this.imageSrc = imageSrc;
    salePrice = productVariation.getSafeSalePrice();
    List<Map<String?, String?>> data =
        product.metaData.map((e) => {e.key: e.value}).toList();
    metaData.addAll(data);
    regularPrice = productVariation.regularPrice?.toString();
    shippingIsTaxable = product.shippingTaxable;
    variationOptions = options.join("; ");
    total = productVariation.price?.toString();
    // FINAL INVESTIGATION - Task 1.2: Set SKU from product (variations inherit parent SKU)
    sku = product.sku; // Can be null, will be handled in .toJson()
    subtotalTax = null; // Will be set to empty string in .toJson()
    appMetaData =
        product.metaData.map((e) => {"key": e.key, "value": e.value}).toList();
  }

  CartLineItem.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    productId = json['product_id'];
    variationId = json['variation_id'];
    quantity = json['quantity'];
    shippingClassId = json['shipping_class_id'].toString();
    taxStatus = json['tax_status'];
    stockQuantity = json['stock_quantity'];
    isManagedStock =
        (json['is_managed_stock'] != null && json['is_managed_stock'] is bool)
            ? json['is_managed_stock']
            : false;
    shippingIsTaxable = json['shipping_is_taxable'];
    subtotal = json['subtotal'];
    total = json['total'];
    taxClass = json['tax_class'];
    stockStatus = json['stock_status'];
    imageSrc = json['image_src'];
    salePrice = json['sale_price'];
    regularPrice = json['regular_price'];
    categories = json['categories'] == null
        ? null
        : List.of(json['categories'] as List)
            .map((e) => MyProductCategory(
                id: e['id'] ?? 0,
                name: e['name'] ?? '',
                slug: e['slug'] ?? '',
                parent: 0,
                count: 0))
            .toList();
    onSale = json['on_sale'];
    variationOptions = json['variation_options'];
    // FINAL INVESTIGATION - Task 1.2: Handle SKU and subtotal_tax from JSON
    sku = json['sku'];
    subtotalTax = json['subtotal_tax'];
    metaData = [];
    if (json['meta_data'] != null) {
      metaData = List.from(json['meta_data']).cast<Map<String, dynamic>>();
    }
    appMetaData = [];
    if (json['app_meta_data'] != null) {
      appMetaData =
          List.from(json['app_meta_data']).cast<Map<String, dynamic>>();
    }
  }

  Map<String, dynamic> toJson() => {
        'name': name,
        'product_id': productId,
        'variation_id': variationId,
        'quantity': quantity,
        'shipping_class_id': shippingClassId,
        'tax_status': taxStatus,
        // INVESTIGATION ZONE 1 - Task 1.2: Ensure tax_class is never null (WooCommerce API requirement)
        'tax_class': taxClass ?? '', // Always empty string, never null for WooCommerce API compliance
        'stock_status': stockStatus,
        'is_managed_stock': isManagedStock,
        'stock_quantity': stockQuantity,
        'shipping_is_taxable': shippingIsTaxable,
        'image_src': imageSrc,
        'categories': categories != null
            ? categories!.map((e) => {'id': e.id, 'name': e.name, 'slug': e.slug}).toList()
            : [],
        'variation_options': variationOptions,
        'subtotal': subtotal,
        'on_sale': onSale,
        'total': total,
        // FINAL INVESTIGATION - Task 1.2: Ensure sku and subtotal_tax are never null (WooCommerce API requirement)
        'sku': sku ?? '', // Always empty string, never null for WooCommerce API compliance
        'subtotal_tax': subtotalTax ?? '', // Always empty string, never null for WooCommerce API compliance
        'meta_data': metaData,
        'app_meta_data': appMetaData,
      };
}
